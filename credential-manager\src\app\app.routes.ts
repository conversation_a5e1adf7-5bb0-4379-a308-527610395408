import { Routes } from '@angular/router';
import { inject } from '@angular/core';
import { AuthService } from './services/auth.service';
import { map } from 'rxjs/operators';

// Auth Guard
const authGuard = () => {
  const authService = inject(AuthService);
  return authService.user$.pipe(
    map(user => !!user)
  );
};

// Redirect Guard (redirect to dashboard if already logged in)
const redirectGuard = () => {
  const authService = inject(AuthService);
  return authService.user$.pipe(
    map(user => !user)
  );
};

export const routes: Routes = [
  {
    path: '',
    redirectTo: '/login',
    pathMatch: 'full'
  },
  {
    path: 'login',
    loadComponent: () => import('./components/auth/login/login').then(m => m.Login),
    canActivate: [redirectGuard]
  },
  {
    path: 'register',
    loadComponent: () => import('./components/auth/register/register').then(m => m.Register),
    canActivate: [redirectGuard]
  },
  {
    path: 'dashboard',
    loadComponent: () => import('./components/dashboard/dashboard').then(m => m.Dashboard),
    canActivate: [authGuard]
  },
  {
    path: '**',
    redirectTo: '/dashboard'
  }
];
